# Suggested Commands for ChromoForge Development

> **🐳 Docker-First Development**: ChromoForge uses Docker Desktop. Access container shell with `./docker-run.sh shell` for all development commands.

## Docker Development Commands
```bash
# Build and start container
./docker-run.sh build
./docker-run.sh start

# Access container shell for development
./docker-run.sh shell

# Inside container:
# Code formatting
black . --line-length 88
isort .

# Type checking
mypy .

# Linting
flake8 .
pylint src/

# Testing
pytest tests/ -v --cov=src/

# Security scanning
bandit -r src/
```

## Database Commands
```bash
# Run database migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > types/database.types.ts

# Reset local database
supabase db reset

# View database logs
supabase logs
```

## File Operations (macOS/Darwin)
```bash
# Find files
find . -name "*.py" -type f

# Search in files
grep -r "pattern" src/

# List directory contents
ls -la

# Change directory
cd /path/to/directory

# View file contents
cat filename.py
head -n 20 filename.py
tail -n 20 filename.py
```

## Development Workflow
```bash
# Start development
./docker-run.sh build
./docker-run.sh start

# Run OCR pipeline
./docker-run.sh process-file "original-pdf-examples/file.pdf"
./docker-run.sh process-batch

# Run tests before commit (inside container)
./docker-run.sh shell
# Inside container:
pytest && black . && mypy .
```

## Git Commands
```bash
git add .
git commit -m "feat: add OCR processing pipeline"
git push origin main
```