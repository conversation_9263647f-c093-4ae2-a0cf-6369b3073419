# ChromoForge OCR Pipeline

A medical OCR pipeline application designed for processing Thai medical documents with PII detection and obfuscation. Uses Google Gemini API for OCR processing and Supabase for secure data storage.

> **🐳 Docker-First Development**: ChromoForge uses Docker Desktop for containerized development. No Python virtual environment setup is required. See [README-Docker.md](README-Docker.md) for detailed Docker usage.

## 🚀 Features

- **Advanced OCR Processing**: Google Gemini 2.0 Flash with enhanced thinking capabilities
- **Enhanced Medical Extraction**: Structured field extraction for medical documents  
- **PII Detection**: Comprehensive Thai PII patterns including National ID, Hospital Numbers, Names, and Phone Numbers
- **PDF Obfuscation**: Multiple obfuscation methods (black box, blur, redact, white box, hash pattern)
- **Database Integration**: Secure Supabase storage with encryption and audit logging
- **Batch Processing**: Concurrent processing of multiple files
- **HIPAA Compliance**: Audit logging and PII encryption
- **Comprehensive Error Handling**: Circuit breakers, retry logic, and graceful degradation

## 📋 Prerequisites

- Docker Desktop
- Google Gemini API key
- Supabase account (optional, for database features)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ChromoForge
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

3. **Build and start with Docker**:
   ```bash
   ./docker-run.sh build
   ./docker-run.sh start
   ```

4. **Test setup**:
   ```bash
   ./docker-run.sh test
   ```

> **Note**: ChromoForge uses Docker Desktop for containerized development. No Python virtual environment setup is required.

## ⚙️ Configuration

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_gemini_api_key_here

# Optional - for database features
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key  
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key

# Optional - application settings
ENABLE_ULTRA_THINK=true
CONFIDENCE_THRESHOLD=0.7
OBFUSCATION_METHOD=black_box
MAX_CONCURRENT_REQUESTS=10
```

## 🚀 Usage

### Basic OCR Processing

```bash
# Process single file
./docker-run.sh process-file "original-pdf-examples/file.pdf"

# Process directory
./docker-run.sh process-batch

# Process with custom settings
./docker-run.sh process-advanced "original-pdf-examples/file.pdf" test-results \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --verbose
```

### Enhanced Features

```bash
# Enable enhanced medical extraction
./docker-run.sh shell
# Inside container:
python -m src.main --input file.pdf --output ./processed --enhanced

# Enable database integration
python -m src.main --input file.pdf --output ./processed --database

# Combine enhanced features with database
python -m src.main --input file.pdf --output ./processed --enhanced --database

# OCR only (no obfuscation)
python -m src.main --input file.pdf --output ./processed --no-obfuscation
```

### Advanced Options

```bash
# Access container shell for advanced usage
./docker-run.sh shell

# Inside container, run full feature example:
python -m src.main \
  --input ./medical-docs \
  --output ./processed \
  --enhanced \
  --database \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --max-concurrent 5 \
  --user-id "user-123" \
  --session-id "session-456" \
  --verbose

# Directory processing with pattern matching
python -m src.main \
  --input ./documents \
  --output ./processed \
  --pattern "*.pdf" \
  --no-recursive \
  --enhanced
```

## 🏗️ Architecture

### Core Structure
```
src/
├── core/           # Core domain models and configuration
│   ├── config.py   # Pydantic settings management
│   ├── models.py   # Domain models (OCRResult, PIIMatch, etc.)
│   ├── constants.py # Application constants
│   └── exceptions.py # Custom exceptions
├── processing/     # Business logic layer
│   ├── ocr_processor.py    # Gemini OCR with ULTRA THINK
│   ├── pii_detector.py     # Thai PII detection patterns
│   ├── pdf_obfuscator.py   # PDF obfuscation methods
│   └── batch_processor.py  # Batch and concurrent processing
├── security/       # Security and compliance
│   ├── pii_encryption.py   # PII encryption utilities
│   └── audit_logger.py     # HIPAA-compliant audit logging
└── utils/          # Utilities and helpers
    ├── logging_config.py   # Structured logging setup
    └── utils.py            # Common utilities
```

### Key Components

1. **OCR Processing**: Google Gemini 2.0 Flash with enhanced thinking capabilities for Thai medical document extraction.

2. **Enhanced Medical Extraction**: Structured field extraction including:
   - Patient codes (TT prefix patterns)
   - Sample codes (6-character alphanumeric)
   - Investigation types (K-TRACK, SPOT-MAS, K4CARE)
   - Patient names (Thai and English)
   - Date of birth (Gregorian and Buddhist Era)
   - Contact information and physician details

3. **PII Detection**: Comprehensive Thai PII patterns including:
   - Thai National ID (13-digit with checksum validation)
   - Hospital Numbers (HN patterns)
   - Lab Numbers (VN patterns)
   - Thai Names (with contextual detection)
   - Phone Numbers (Thai mobile formats)
   - Medical Record Numbers

4. **PDF Obfuscation**: Multiple methods:
   - BLACK_BOX: Solid black rectangles
   - BLUR: Gaussian blur
   - REDACT: Text replacement
   - WHITE_BOX: White rectangles
   - HASH_PATTERN: Pattern-based hashing

5. **Database Integration**: Supabase architecture with:
   - Encrypted PII storage using pgcrypto
   - Row-level security (RLS) policies
   - Audit logging for compliance
   - Soft deletes for data recovery

## 🧪 Testing

### Run Tests

```bash
# Access container shell
./docker-run.sh shell

# Inside container, run tests:
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest -m integration  # Real integration tests
pytest -m performance  # Performance tests
pytest -m security     # Security tests
pytest -m slow         # Slow tests

# Run specific test file
pytest tests/test_end_to_end_real.py -v
```

### Test Coverage

The project has comprehensive test coverage including:
- **Unit Tests**: All core modules (17 test files)
- **Integration Tests**: Real API calls and database operations
- **Performance Tests**: Benchmark tests for OCR processing
- **Security Tests**: PII detection and encryption validation
- **End-to-End Tests**: Full pipeline testing with real PDFs

Current test modules:
- Core configuration and models
- OCR processing with Gemini integration
- PII detection patterns
- PDF obfuscation methods
- Batch processing
- Security encryption and audit logging
- Database integration
- Utility functions

## 🔧 Development Workflow

### Code Quality

```bash
# Access container shell
./docker-run.sh shell

# Inside container, run code quality tools:
black src/ tests/

# Sort imports
isort src/ tests/

# Run linting
flake8 src/ tests/

# Security scanning
bandit -r src/

# Type checking
mypy src/
```

### Adding New Features

1. Update models in `src/core/models.py`
2. Implement logic in appropriate `src/processing/` module
3. Add tests in `tests/`
4. Update documentation

### Modifying PII Patterns

1. Edit regex patterns in `src/processing/pii_detector.py`
2. Update corresponding tests
3. Consider performance impact of complex patterns

### Database Changes

1. Create migration SQL in `migrations/` directory
2. Test with both upgrade and rollback scenarios
3. Update RLS policies as needed

## 🔒 Security Considerations

1. **PII Protection**: All PII is encrypted at rest using pgcrypto with organization-specific keys
2. **Audit Logging**: All data access and modifications are logged for HIPAA compliance
3. **Soft Deletes**: Data is marked as deleted but retained for recovery
4. **Role-Based Access**: Granular permissions based on user roles
5. **File Validation**: Strict file size and format validation

## 📊 Performance

- **Concurrent Processing**: Up to 10 concurrent requests by default
- **Circuit Breakers**: Automatic failure detection and recovery
- **Retry Logic**: Exponential backoff for failed operations
- **Caching**: Intelligent caching for repeated operations
- **Batch Optimization**: Efficient processing of multiple files

## 🐛 Troubleshooting

### Common Issues

1. **Missing API Key**: Ensure `GOOGLE_API_KEY` is set in `.env`
2. **Database Connection**: Check Supabase credentials if using `--database`
3. **File Permissions**: Ensure read access to input files and write access to output directory
4. **Memory Issues**: Reduce `--max-concurrent` for large files

### Debug Mode

```bash
# Enable verbose logging
./docker-run.sh process-advanced "original-pdf-examples/file.pdf" test-results --verbose

# Check environment setup
./docker-run.sh test

# Access container for debugging
./docker-run.sh shell
```

## 📝 CLI Reference

### Required Arguments
- `--input, -i`: Input PDF file or directory path
- `--output, -o`: Output directory for processed files

### Feature Flags
- `--enhanced`: Enable enhanced medical extraction with structured fields
- `--database, --save-to-db`: Enable database integration (requires Supabase setup)
- `--no-obfuscation`: Disable PDF obfuscation (OCR and PII detection only)

### Processing Options
- `--pattern`: File pattern for directory processing (default: *.pdf)
- `--no-recursive`: Disable recursive directory scanning
- `--confidence-threshold`: PII detection confidence threshold (0.0-1.0)
- `--obfuscation-method`: Obfuscation method (black_box, blur, redact, white_box, hash_pattern)
- `--max-concurrent`: Maximum concurrent processing threads

### Utility Options
- `--user-id`: User ID for audit logging
- `--session-id`: Session ID for tracking
- `--organization-id`: Organization ID for database operations
- `--disable-ultra-think`: Disable enhanced OCR processing
- `--verbose, -v`: Enable verbose logging
- `--version`: Show version information

## 📄 License

[Add license information here]

## 🤝 Contributing

[Add contributing guidelines here]

## 📞 Support

[Add support information here]